const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // Only proxy API calls, not static files
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:8080',
      changeOrigin: true,
      timeout: 10000,
      onError: (err, req, res) => {
        console.log('Proxy Error:', err.message);
        res.status(503).json({
          error: 'API Gateway unavailable',
          message: 'Please ensure API Gateway is running on port 8080'
        });
      },
      onProxyReq: (proxyReq, req, res) => {
        console.log(`Proxying ${req.method} ${req.url} to API Gateway`);
      }
    })
  );

  // Proxy auth routes
  app.use(
    '/auth',
    createProxyMiddleware({
      target: 'http://localhost:8080',
      changeOrigin: true,
      timeout: 10000,
      onError: (err, req, res) => {
        console.log('Auth Proxy Error:', err.message);
        res.status(503).json({
          error: 'Auth service unavailable'
        });
      }
    })
  );

  // Proxy user routes
  app.use(
    '/users',
    createProxyMiddleware({
      target: 'http://localhost:8080',
      changeOrigin: true,
      timeout: 10000,
      onError: (err, req, res) => {
        console.log('Users Proxy Error:', err.message);
        res.status(503).json({
          error: 'User service unavailable'
        });
      }
    })
  );
};
