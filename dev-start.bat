@echo off
echo ========================================
echo 🔥 Starting Development Environment
echo ========================================

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

echo ✅ Docker is running

REM Start development environment with hot reload
echo 🚀 Starting services in development mode...
docker-compose -f docker-compose.dev.yml up -d

echo.
echo ✅ Development environment started!
echo.
echo 📋 Access your services:
echo 🌐 Frontend:     http://localhost:3000
echo 🔗 API Gateway:  http://localhost:8080
echo 🔐 Auth Service: http://localhost:3001
echo 🔗 Link Service: http://localhost:3002
echo 👥 Community:    http://localhost:3003
echo 💬 Chat Service: http://localhost:3004
echo 📰 News Service: http://localhost:3005
echo ⚙️  Admin:       http://localhost:3006
echo.
echo 🔧 Development Commands:
echo   dev-logs.bat     - View logs
echo   dev-stop.bat     - Stop services
echo   dev-restart.bat  - Restart services
echo.
echo 💡 Code changes will auto-reload (no rebuild needed)!
echo.
pause
