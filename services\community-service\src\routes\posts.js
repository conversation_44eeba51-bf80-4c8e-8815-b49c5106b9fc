const express = require('express');
const router = express.Router();
const { db, collections } = require('../config/firebase');
const Logger = require('../../shared/utils/logger');

const logger = new Logger('community-service');

// Get posts from Firestore
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      sort = 'trending',
      category = 'all',
      search = '',
      newsOnly = 'false',
      userPostsOnly = 'false',
      includeNews = 'true'
    } = req.query;

    logger.info('Fetching posts', {
      page: parseInt(page),
      limit: parseInt(limit),
      sort,
      category,
      search,
      newsOnly,
      userPostsOnly,
      includeNews
    });

    // Simplified Firestore query with timeout
    const queryTimeout = 5000; // 5 seconds timeout

    let posts = [];
    let total = 0;

    try {
      // Simple query without complex filtering to avoid hanging
      let query = db.collection(collections.POSTS);

      // Only add basic sorting
      if (sort === 'newest') {
        query = query.orderBy('createdAt', 'desc');
      } else {
        // Default to creation time for trending
        query = query.orderBy('createdAt', 'desc');
      }

      // Add pagination
      query = query.limit(parseInt(limit));

      // Execute with timeout
      const queryPromise = query.get();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Query timeout')), queryTimeout)
      );

      const snapshot = await Promise.race([queryPromise, timeoutPromise]);

      // Transform documents
      posts = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
        };
      });

      total = posts.length; // Simplified total count

      logger.info('Posts fetched from Firestore', {
        count: posts.length,
        hasData: posts.length > 0
      });

    } catch (firestoreError) {
      logger.warn('Firestore query failed, using fallback', {
        error: firestoreError.message
      });
      throw firestoreError; // Let it fall through to catch block
    }

    // Calculate pagination info
    const totalPages = Math.ceil(total / parseInt(limit));
    const hasNext = parseInt(page) < totalPages;
    const hasPrev = parseInt(page) > 1;

    logger.info('Posts fetched successfully', {
      count: posts.length,
      total,
      page: parseInt(page),
      totalPages
    });

    res.json({
      success: true,
      data: {
        posts,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext,
          hasPrev
        }
      }
    });

  } catch (error) {
    logger.error('Error fetching posts', { error: error.message });

    // Fallback to mock data
    res.json({
      success: true,
      data: {
        posts: [
          {
            id: 'mock-1',
            title: 'Cảnh báo: Trang web lừa đảo mới được phát hiện',
            content: 'Một trang web giả mạo ngân hàng đã được phát hiện. Hãy cẩn thận khi nhập thông tin cá nhân...',
            author: {
              email: '<EMAIL>',
              displayName: 'Người dùng ẩn danh'
            },
            type: 'user_post',
            category: 'phishing',
            voteStats: {
              safe: 2,
              unsafe: 15,
              suspicious: 3,
              total: 20
            },
            voteScore: 10, // unsafe votes weighted higher
            commentCount: 5,
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
            updatedAt: new Date().toISOString(),
            tags: ['phishing', 'banking', 'scam'],
            url: 'https://suspicious-bank-site.com',
            verified: false
          },
          {
            id: 'mock-2',
            title: 'Chia sẻ: Cách nhận biết email lừa đảo',
            content: 'Dựa trên kinh nghiệm cá nhân, tôi muốn chia sẻ một số dấu hiệu nhận biết email lừa đảo...',
            author: {
              email: '<EMAIL>',
              displayName: 'Chuyên gia bảo mật'
            },
            type: 'user_post',
            category: 'education',
            voteStats: {
              safe: 25,
              unsafe: 1,
              suspicious: 2,
              total: 28
            },
            voteScore: 22,
            commentCount: 12,
            createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
            updatedAt: new Date().toISOString(),
            tags: ['education', 'email', 'tips'],
            verified: true
          }
        ],
        pagination: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      },
      fallback: true,
      error: error.message
    });
  }
});

// Create new post
router.post('/', async (req, res) => {
  try {
    const { title, content, category = 'general', url, tags = [] } = req.body;

    // Basic validation
    if (!title || !content) {
      return res.status(400).json({
        success: false,
        error: 'Title and content are required'
      });
    }

    // Get user info from auth middleware (if implemented)
    const userId = req.user?.uid || 'anonymous';
    const userEmail = req.user?.email || '<EMAIL>';
    const userDisplayName = req.user?.displayName || 'Người dùng ẩn danh';

    // Create post document
    const postData = {
      title: title.trim(),
      content: content.trim(),
      author: {
        uid: userId,
        email: userEmail,
        displayName: userDisplayName
      },
      type: 'user_post',
      category,
      url: url || null,
      tags: Array.isArray(tags) ? tags : [],
      voteStats: {
        safe: 0,
        unsafe: 0,
        suspicious: 0,
        total: 0
      },
      voteScore: 0,
      commentCount: 0,
      verified: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Save to Firestore
    const docRef = await db.collection(collections.POSTS).add(postData);

    logger.info('Post created successfully', {
      postId: docRef.id,
      userId,
      title: title.substring(0, 50)
    });

    // Return created post
    res.status(201).json({
      success: true,
      message: 'Post created successfully',
      data: {
        post: {
          id: docRef.id,
          ...postData,
          createdAt: postData.createdAt.toISOString(),
          updatedAt: postData.updatedAt.toISOString()
        }
      }
    });

  } catch (error) {
    logger.error('Error creating post', { error: error.message });

    res.status(500).json({
      success: false,
      error: 'Failed to create post',
      details: error.message
    });
  }
});

router.get('/:id', (req, res) => {
  res.json({
    success: true,
    post: {
      id: req.params.id,
      title: 'Suspicious website reported',
      content: 'Found this suspicious website that looks like a phishing attempt...',
      author: '<EMAIL>',
      votes: 5,
      comments: 2,
      createdAt: new Date().toISOString()
    }
  });
});

module.exports = router;
