# Development Dockerfile Template
# Supports hot reload for development
FROM node:18-alpine

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Create app directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Install nodemon globally for hot reload
RUN npm install -g nodemon

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Make sure shared directory is accessible
RUN mkdir -p /app/shared

# Change ownership to nodejs user
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port (will be overridden by individual services)
EXPOSE 3000

# Use nodemon for development with hot reload
CMD ["dumb-init", "nodemon", "--watch", "src", "--watch", "package.json", "--watch", "../shared", "src/app.js"]
