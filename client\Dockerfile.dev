# Development Dockerfile for React Frontend
# Supports hot reload for development
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dependencies for node-gyp
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Change ownership
RUN chown -R node:node /app
USER node

# Expose port
EXPOSE 3000

# Start development server with hot reload
CMD ["npm", "start"]
