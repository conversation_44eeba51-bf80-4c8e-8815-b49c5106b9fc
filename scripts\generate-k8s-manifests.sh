#!/bin/bash

# =============================================================================
# 🚀 Kubernetes Manifests Generator for Anti-Fraud Platform
# Automatically generates K8s manifests for all microservices
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m'

# Emojis
ROCKET="🚀"
CHECK="✅"
WARNING="⚠️"
ERROR="❌"
INFO="ℹ️"
GEAR="⚙️"

# Print functions
print_header() {
    echo -e "\n${WHITE}${ROCKET} $1${NC}"
    echo -e "${WHITE}============================================================${NC}"
}

print_success() {
    echo -e "${GREEN}${CHECK} $1${NC}"
}

print_error() {
    echo -e "${RED}${ERROR} $1${NC}"
}

print_info() {
    echo -e "${BLUE}${INFO} $1${NC}"
}

# Configuration
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
K8S_DIR="$PROJECT_ROOT/k8s"
MANIFESTS_DIR="$K8S_DIR/manifests"
TEMPLATE_FILE="$K8S_DIR/service-template.yml"

# Service definitions
declare -A SERVICES=(
    ["api-gateway"]="8080"
    ["auth-service"]="3001"
    ["link-service"]="3002"
    ["community-service"]="3003"
    ["chat-service"]="3004"
    ["news-service"]="3005"
    ["admin-service"]="3006"
    ["frontend"]="3000"
)

# Docker registry (modify this to your registry)
DOCKER_REGISTRY="${DOCKER_REGISTRY:-ghcr.io/your-org/anti-fraud-platform}"

# Create manifests directory
mkdir -p "$MANIFESTS_DIR"

echo "Generating Kubernetes manifests for microservices..."

# Generate manifests for each service
for service in "${!services[@]}"; do
    port=${services[$service]}
    echo "Generating manifest for $service (port: $port)"
    
    # Copy template and replace placeholders
    sed "s/{{SERVICE_NAME}}/$service/g; s/{{PORT}}/$port/g" k8s/service-template.yml > "k8s/services/$service.yml"
    
    echo "Generated k8s/services/$service.yml"
done

echo "All Kubernetes manifests generated successfully!"

# Generate API Gateway specific configuration
echo "Generating API Gateway ingress..."

cat > k8s/ingress.yml << EOF
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-gateway-ingress
  namespace: anti-fraud-platform
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.anti-fraud-platform.com
    secretName: api-gateway-tls
  rules:
  - host: api.anti-fraud-platform.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 8080
EOF

echo "Generated k8s/ingress.yml"

# Generate deployment script
cat > scripts/deploy-k8s.sh << 'EOF'
#!/bin/bash

# Deploy all microservices to Kubernetes

set -e

echo "Deploying Anti-Fraud Platform to Kubernetes..."

# Apply namespace
kubectl apply -f k8s/namespace.yml

# Apply ConfigMap and Secrets
kubectl apply -f k8s/configmap.yml

# Apply Redis
kubectl apply -f k8s/redis.yml

# Wait for Redis to be ready
echo "Waiting for Redis to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/redis -n anti-fraud-platform

# Apply all services
for file in k8s/services/*.yml; do
    echo "Applying $file"
    kubectl apply -f "$file"
done

# Apply ingress
kubectl apply -f k8s/ingress.yml

# Wait for all deployments to be ready
echo "Waiting for all deployments to be ready..."
kubectl wait --for=condition=available --timeout=600s deployment --all -n anti-fraud-platform

echo "Deployment completed successfully!"

# Show status
echo "Current status:"
kubectl get pods -n anti-fraud-platform
kubectl get services -n anti-fraud-platform
kubectl get ingress -n anti-fraud-platform
EOF

chmod +x scripts/deploy-k8s.sh
echo "Generated scripts/deploy-k8s.sh"

echo "Kubernetes setup completed!"
echo ""
echo "To deploy to Kubernetes:"
echo "1. Update secrets in k8s/configmap.yml"
echo "2. Run: ./scripts/deploy-k8s.sh"
