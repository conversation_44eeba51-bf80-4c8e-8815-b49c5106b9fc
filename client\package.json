{"name": "factcheck-frontend", "version": "1.0.0", "description": "FactCheck Frontend Application", "private": true, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.1", "@tanstack/react-query": "^5.80.6", "axios": "^1.5.0", "clsx": "^2.1.1", "firebase": "^10.5.0", "framer-motion": "^12.15.0", "gsap": "^3.13.0", "js-cookie": "^3.0.5", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.5.2", "react-query": "^3.39.3", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "styled-components": "^6.0.7", "tailwind-merge": "^3.3.0", "yup": "^1.3.2"}, "devDependencies": {"@types/js-cookie": "^3.0.3", "web-vitals": "^3.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:render": "npm ci && npm run build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8080"}